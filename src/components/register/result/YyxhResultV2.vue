<template>
  <section class="main fixed white_bg" style="position: fixed">
    <t-header @back="back" />
    <article class="content" v-show="showDetail">
      <div class="result_page" v-if="pageType === '1'">
        <div class="result_tips">
          <div class="icon ok" />
          <h5><span>转销户预约</span>撤销成功</h5>
        </div>
        <div class="result_info">
          <ul>
            <li>
              <span class="tit">转销户预约</span>
              <p><span class="state">撤销成功</span></p>
            </li>
          </ul>
        </div>
      </div>
      <div class="result_page" v-else-if="['0', '2'].includes(pageType)">
        <div class="result_tips">
          <div class="icon ok" />
          <h5><span>转销户预约</span>初审通过</h5>
        </div>
        <div class="result_info">
          <ul>
            <li>
              <span class="tit">转销户预约</span>
              <p><span class="state">预约成功</span></p>
            </li>
          </ul>
        </div>
      </div>
      <div class="result_page" v-else>
        <div class="result_tips">
          <div class="icon ing" />
          <h5><span>成功提交，</span>处理中</h5>
          <p>
            我司工作人员会尽快审核您的业务申请，审核通过后，您需上传本人身份证件资料，完成正式销户视频认证。请您保持手机畅通，并关注审核结果短信通知。如有疑问，请咨询您的服务人员或<span
              class="no-wrap"
              >95310</span
            >。
          </p>
        </div>
        <div class="result_info">
          <ul>
            <li>
              <span class="tit">转销户预约</span>
              <p><span class="state">处理中</span></p>
            </li>
          </ul>
        </div>
      </div>
    </article>
    <footer class="footer white_bg">
      <div class="ce_btn black">
        <a
          v-show="['0', '2'].includes(pageType)"
          class="p_button"
          @click="nextStep"
          >下一步</a
        >
        <a
          v-show="['0', '2'].includes(pageType)"
          class="p_button border"
          @click="popupTaskCannel"
          >撤销转销户预约</a
        >
        <a v-show="pageType === '1'" class="p_button border" @click="flowEnd"
          >返回</a
        >
        <a v-show="pageType === '3'" class="p_button" @click="popupTaskCannel"
          >我不销户了</a
        >
        <a
          v-show="['', '3'].includes(pageType)"
          class="p_button border"
          @click="back"
          >返回</a
        >
      </div>
    </footer>
  </section>
</template>

<script>
import {
  xhFlowInfoQryV2,
  detainTaskCancel,
  invalidFlowIns,
  xhDetainTaskCreate,
  flowQueryIns
} from '@/service/service';
import { EVENT_NAME } from '@/common/formEnum';

export default {
  name: 'YyxhResultV2',
  inject: ['tkFlowInfo', 'eventMessage'],
  data() {
    return {
      showDetail: false,
      xhAutoBizType: '014001', //销户自动化业务编号
      flowInsId: "",
      pageType: '0' //0挽留失败，1挽留成功，2无需挽留 3挽留处理中
    };
  },
  watch: {},
  computed: {
    isApp() {
      return $hvue.platform !== '0';
    }
  },
  created() {},
  mounted() {
    this.renderingView();
  },
  methods: {
    renderingView() {
      const flowToken = sessionStorage.getItem('TKFlowToken');
      flowQueryIns({ flowToken }).then((res) => {
        this.flowInsId = res.data.id;
      })
      xhFlowInfoQryV2({ flowToken })
        .then(({ data }) => {
          // 挽回状态: 0挽留失败，1挽留成功，2无需挽留 3挽留处理中
          const { conclusion = '' } = data;
          this.dataResult = data;
          this.pageType = conclusion;
          if (conclusion === '') {
            // 如果没有挽留状态的，创建一个预约销户单
            xhDetainTaskCreate({ flowToken }).then((data) => {
              console.log('xhDetainTaskCreate response === ', data);
            });
          }
          this.showDetail = true;
        })
        .catch(() => {
          this.showDetail = false;
        });
    },
    flowEnd() {
      invalidFlowIns({
        flowInsId: this.flowInsId
      })
        .then(({ code, msg }) => {
          if (code === 0) {
            this.back();
          } else {
            return Promise.reject(msg);
          }
        })
        .catch((err) => {
          this.$TAlert({
            tips: err
          });
        });
    },
    popupTaskCannel() {
      const _this = this;
      _this.$TAlert({
        title: '提示',
        tips: '点击“确认”，即终止本次销户业务办理，如果您有任何意见或建议，欢迎致电95310向我们反馈。',
        hasCancel: true,
        confirmBtn: '确认',
        cancelBtn: '取消',
        confirm: () => {
          _this.taskCannel();
        }
      });
    },
    taskCannel() {
      const _this = this;
      detainTaskCancel({
        preFlowInsId: this.flowInsId,
        flowToken: sessionStorage.getItem('TKFlowToken')
      })
        .then(({ code, msg }) => {
          if (code === 0) {
            _this.back();
          } else {
            return Promise.reject(msg);
          }
        })
        .catch((err) => {
          _this.$TAlert({
            tips: err
          });
        });
    },
    back() {
      this.eventMessage(this, EVENT_NAME.TO_INDEX);
    },
    nextStep() {
      this.eventMessage(this, EVENT_NAME.NEXT_STEP);
    }
  }
};
</script>
