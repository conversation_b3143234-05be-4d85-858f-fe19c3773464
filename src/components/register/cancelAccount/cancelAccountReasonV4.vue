<template>
  <fragment>
    <!-- 页面标题和说明 -->
    <div class="com_title">
      <h5>
        感恩与您携手的美好时光，衷心希望您能告诉我们销户辞别的原因，让未来更好的国金与您再相逢。
      </h5>
    </div>

    <!-- 销户原因选择区域 -->
    <div class="cm_sele_wrap">
      <h5 class="title">请选择您转销户的原因(支持多选)∶</h5>

      <!-- 原因列表 -->
      <ul class="cm_sele_list reason-list">
        <fragment
          v-for="(parentReason, parentIndex) in cancelReasonList"
          :key="parentIndex"
        >
          <!-- 父级原因项 -->
          <li @click="handleReasonSelection(parentReason)">
            <div class="layout">
              <span
                class="icon_check"
                :class="{ checked: parentReason.isChecked }"
              ></span>
              <p>{{ parentReason.displayText }}</p>
            </div>
          </li>

          <!-- 子级原因项 -->
          <li
            v-for="(childReason, childIndex) in parentReason.children"
            :key="`${parentIndex}-${childIndex}`"
            class="sub_item sub-reason-item"
            @click="handleReasonSelection(childReason)"
          >
            <div class="layout">
              <span
                class="icon_check"
                :class="{ checked: childReason.isChecked }"
              ></span>
              <p>{{ childReason.displayText }}</p>
            </div>
          </li>
        </fragment>
      </ul>

      <!-- 其他原因输入框 -->
      <div class="notes_input" v-if="shouldShowOtherInput">
        <textarea
          placeholder="请输入"
          maxlength="100"
          v-model="otherReasonText"
        ></textarea>
      </div>
    </div>
  </fragment>
</template>

<script>
import { queryDictProps } from '@/common/util';
import { EVENT_NAME } from '@/common/formEnum';

export default {
  name: 'CancelAccountReasonV4',
  inject: ['eventMessage'],
  data() {
    return {
      cancelReasonList: [], // 销户原因列表
      otherReasonText: '', // 其他原因文本内容
      // 性能优化：缓存计算结果和快速查找
      cachedSelectedReasons: null,
      cachedHasSelection: null,
      reasonMap: new Map(), // 原因项快速查找映射表
      otherReasonItem: null // 缓存"其他"原因项
    };
  },
  watch: {
    // 监听其他原因文本输入，过滤emoji表情
    otherReasonText: {
      handler(newValue) {
        // emoji表情正则表达式
        const emojiRegex =
          /[\uD83C|\uD83D|\uD83E][\uDC00-\uDFFF][\u200D|\uFE0F]|[\uD83C|\uD83D|\uD83E][\uDC00-\uDFFF]|[0-9|*|#]\uFE0F\u20E3|[0-9|#]\u20E3|[\u203C-\u3299]\uFE0F\u200D|[\u203C-\u3299]\uFE0F|[\u2122-\u2B55]|\u303D|[A9|AE]\u3030|\uA9|\uAE|\u3030/gi;

        if (emojiRegex.test(newValue)) {
          this.otherReasonText = newValue.replace(emojiRegex, '');
          console.log('过滤emoji表情:', newValue);
        }
      }
    },

    // 监听下一步按钮状态变化
    isNextButtonEnabled: {
      handler(isEnabled) {
        if (isEnabled) {
          const formData = {
            reason_acc_cancel: this.selectedReasonsText,
            reason_acc_cancel_other: this.shouldShowOtherInput
              ? this.otherReasonText
              : ''
          };
          this.$emit('change', formData);
          this.eventMessage(this, EVENT_NAME.NEXT_BTN, {
            btnStatus: 1
          });
        } else {
          this.eventMessage(this, EVENT_NAME.NEXT_BTN, {
            btnStatus: 0
          });
        }
      },
      deep: true,
      immediate: true
    }
  },
  computed: {
    // 计算下一步按钮是否可用（性能优化：减少重复计算）
    isNextButtonEnabled() {
      // 检查是否有选中的原因
      if (!this.hasSelectedReason) {
        return false;
      }

      // 如果显示其他原因输入框，需要填写内容
      return !this.shouldShowOtherInput || this.otherReasonText.trim() !== '';
    },

    // 检查是否有选中的原因（性能优化：避免重复遍历）
    hasSelectedReason() {
      for (const parentReason of this.cancelReasonList) {
        if (parentReason.isChecked) {
          // 如果父级原因有子项，需要至少选择一个子项
          if (parentReason.children?.length > 0) {
            if (parentReason.children.some((child) => child.isChecked)) {
              return true;
            }
          } else {
            return true;
          }
        }
      }
      return false;
    },

    // 获取已选择的原因文本（性能优化：使用for循环替代forEach）
    selectedReasonsText() {
      const selectedReasons = [];

      for (const parentReason of this.cancelReasonList) {
        if (parentReason.isChecked) {
          if (parentReason.children?.length > 0) {
            // 有子项的情况，收集选中的子项
            for (const childReason of parentReason.children) {
              if (childReason.isChecked) {
                selectedReasons.push(
                  `${parentReason.displayText},${childReason.displayText}`
                );
              }
            }
          } else {
            // 没有子项的情况，直接添加父项
            selectedReasons.push(parentReason.displayText);
          }
        }
      }

      return selectedReasons.join(';');
    },

    // 判断是否应该显示其他原因输入框（性能优化：使用缓存）
    shouldShowOtherInput() {
      return this.otherReasonItem?.isChecked || false;
    }
  },
  created() {
    // 设置页面背景为白色
    this.$store.commit('flow/setWhiteBg', true);
  },

  mounted() {
    this.loadCancelReasons();
  },
  methods: {
    // 加载销户原因数据
    async loadCancelReasons() {
      try {
        const reasonData = await queryDictProps('bc.common.closeAccReason');
        this.cancelReasonList = this.buildReasonHierarchy(reasonData);
      } catch (error) {
        this.$TAlert({
          tips: error
        });
      }
    },

    // 构建原因层级结构（性能优化：一次遍历完成分类和处理）
    buildReasonHierarchy(reasonList) {
      const parentReasons = [];
      const childReasons = [];

      // 一次遍历完成分类和dictLabel处理
      for (const reason of reasonList) {
        const processedReason = this.processReasonItem(reason);

        if (reason.dictValue.length === 2) {
          parentReasons.push({
            ...processedReason,
            children: [],
            isChecked: false
          });
        } else if (reason.dictValue.length === 4) {
          childReasons.push({
            ...processedReason,
            isChecked: false
          });
        }
      }

      // 创建父级原因映射表，便于快速查找
      const parentReasonMap = new Map();
      for (const parentReason of parentReasons) {
        parentReasonMap.set(parentReason.dictValue, parentReason);

        // 缓存"其他"原因项
        if (parentReason.dictValue === '99') {
          this.otherReasonItem = parentReason;
        }
      }

      // 将子级原因分配到对应的父级原因下
      for (const childReason of childReasons) {
        const parentValue = childReason.dictValue.substring(0, 2);
        const parentReason = parentReasonMap.get(parentValue);

        if (parentReason) {
          parentReason.children.push(childReason);
        }
      }

      // 构建快速查找映射表
      this.buildReasonMap(parentReasons);

      return parentReasons;
    },

    // 处理原因项：分离展示文案和弹窗文案
    processReasonItem(reason) {
      const [displayText, warningText] = reason.dictLabel.split('|');

      return {
        ...reason,
        displayText: displayText.trim(),
        warningText: warningText?.trim() || null
      };
    },

    // 构建原因项快速查找映射表
    buildReasonMap(parentReasons) {
      this.reasonMap.clear();

      for (const parentReason of parentReasons) {
        this.reasonMap.set(parentReason.dictValue, parentReason);

        for (const childReason of parentReason.children) {
          this.reasonMap.set(childReason.dictValue, childReason);
        }
      }
    },
    // 处理原因选择前的预检查（可能包含警告信息）
    handleReasonSelection(reasonItem) {
      // 如果是取消勾选操作，直接执行，无需弹窗
      if (reasonItem.isChecked) {
        this.toggleReasonSelection(reasonItem);
        return;
      }

      // 如果是勾选操作且有警告信息，则弹窗确认
      if (reasonItem.warningText) {
        this.showWarningDialog(reasonItem.warningText, reasonItem);
      } else {
        this.toggleReasonSelection(reasonItem);
      }
    },

    // 显示警告对话框
    showWarningDialog(warningMessage, reasonItem) {
      this.$TAlert({
        title: '温馨提示',
        tips: warningMessage,
        hasCancel: true,
        confirmBtn: '取消销户',
        cancelBtn: '继续销户',
        cancel: () => this.toggleReasonSelection(reasonItem)
      });
    },

    // 切换原因选择状态（性能优化：清除缓存）
    toggleReasonSelection(reasonItem) {
      this.$set(reasonItem, 'isChecked', !reasonItem.isChecked);

      // 清除缓存，强制重新计算
      this.clearCache();

      if (this.isParentReason(reasonItem)) {
        this.handleParentReasonToggle(reasonItem);
      } else if (this.isChildReason(reasonItem)) {
        this.handleChildReasonToggle(reasonItem);
      }
    },

    // 清除计算缓存
    clearCache() {
      this.cachedSelectedReasons = null;
      this.cachedHasSelection = null;
    },

    // 判断是否为父级原因
    isParentReason(reasonItem) {
      return reasonItem.children && reasonItem.children.length > 0;
    },

    // 判断是否为子级原因
    isChildReason(reasonItem) {
      return reasonItem.dictValue.length === 4;
    },

    // 处理父级原因切换
    handleParentReasonToggle(parentReason) {
      if (parentReason.children?.length > 0) {
        // 如果父级原因被选中，则所有子项全选
        if (parentReason.isChecked) {
          parentReason.children.forEach((childReason) => {
            this.$set(childReason, 'isChecked', true);
          });
        } else {
          // 如果父级原因被取消选中，则所有子项取消全选
          parentReason.children.forEach((childReason) => {
            this.$set(childReason, 'isChecked', false);
          });
        }
      }
    },

    // 处理子级原因切换（性能优化：使用Map快速查找）
    handleChildReasonToggle(childReason) {
      const parentValue = childReason.dictValue.substring(0, 2);
      const parentReason = this.reasonMap.get(parentValue);

      if (parentReason) {
        // 检查是否有任何子项被选中
        const hasSelectedChild = parentReason.children.some(
          (child) => child.isChecked
        );

        // 父项状态逻辑：只要有子项被选中，父项就选中
        this.$set(parentReason, 'isChecked', hasSelectedChild);
      }
    }
  }
};
</script>
<style scoped>
/* 父级原因项样式 */
.reason-list li:not(.sub-reason-item) {
  padding: 0.15rem 0;
}

/* 子级原因项样式 */
.sub-reason-item {
  padding: 0.1rem 0 0.1rem 0.2rem;
}

/* 页面编号样式 */
h5.title > .page_num {
  width: 0.35rem;
  height: 0.22rem;
  display: inline-flex;
  background-color: #fa443a;
  color: #ffffff;
  font-size: 0.14rem;
  padding-left: 0.05rem;
  margin-right: 0.1rem;
}
</style>
