/**
 * @desc 接口请求定义
 * <AUTHOR>
 */
import { baseService } from './baseService';
import { axiosRequest } from './axiosService';
import { trackEvent } from '@/common/util';
let requestURL = $hvue.customConfig.serverUrl;

/**
 * 所有请求的公用方法 可以添加公用参数 或者其他公用操作
 * @param {String} funcNo 功能号
 * @param {Object} params 接口的业务参数
 * @param {Object} options 此次请求的相关设置 如 请求方法 是否加签， 具体定义见netIntercept.js
 */
function commService(funcNo, params = {}, options = {}) {
  let url = requestURL;
  if (funcNo) {
    url += `/${funcNo}`;
  }
  return baseService({
    url,
    params,
    options
  }).then(
    (res) => {
      return Promise.resolve(res);
    },
    (err) => {
      trackEvent({
        // event_name: 'ywbl_view',
        event_name: 'ywbl_show', // 曝光弹框
        page_name: '',
        module_name: '报错提示',
        element_name: 'error_popup',
        remarks: JSON.stringify(err)
      });
      return Promise.reject(err);
    }
  );
}

/**
 * @desc 业务公用接口查询数据字典
 * @param {Object} params 业务参数
 * @param {Object} options 接口请求相关设置 参考第一个接口的说明
 */
export function getDictData(params) {
  return commService('common/dict/map', params, {
    loading: false,
    method: 'GET',
    allowRepeat: true
  });
}

/**
 * @desc 业务公用接口查询省市区
 * @param {Object} params 业务参数
 * @param {Object} options 接口请求相关设置 参考第一个接口的说明
 */
export function getAdressTree(params, options = {}) {
  return commService(
    'common/address/tree',
    params,
    Object.assign(
      {
        method: 'GET'
      },
      options
    )
  );
}

/**
 * @desc 业务实例查询
 * @param {Object} params 业务参数
 * @param {Object} options 接口请求相关设置 参考第一个接口的说明
 */
export function flowQueryIns(params, options = {}) {
  return commService('flow/queryIns', params, options);
}

/**
 * @desc 受理单提交,结果页相关
 * @param {Object} params 业务参数
 */
export function flowSubmit(params, options = {}) {
  return commService('flow/submit', params, options);
}

/**
 * @desc 受理单提交,结果页相关
 * @param {Object} params 业务参数
 */
export function flowEnd(params, options = {}) {
  return commService('flow/end', params, options);
}

/**
 * @desc 受理单提交,结果页相关
 * @param {Object} params 业务参数
 */
export function flowInsInvalid(params, options = {}) {
  return commService('flow/flowInsInvalid', params, options);
}

/**
 * @desc 客户身份认证
 * @param {Object} params 业务参数
 */
export function matchClientInfo(params) {
  return commService('client/matchClientInfo', params);
}

/**
 * @desc 网信手机号码核查
 * @param {Object} params 业务参数
 */
export function clientMobileCheck(params, options = {}) {
  return commService(
    'client/clientMobileCheck',
    params,
    Object.assign(
      {
        method: 'GET'
      },
      options
    )
  );
}

/**
 * @desc 客户基本信息查询
 * @param {Object} params 业务参数
 */
export function clientInfoQry(params, options = {}) {
  return commService(
    'client/clientInfoQry',
    params,
    Object.assign(
      {
        method: 'GET'
      },
      options
    )
  );
}

/**
 * @desc 控制人受益人信息查询
 * @param {Object} params 业务参数
 */
export function clientRelatedPersonInfoQty(params, options = {}) {
  return commService(
    'client/clientRelatedPersonInfoQty',
    params,
    Object.assign(
      {
        method: 'GET'
      },
      options
    )
  );
}

/**
 * @desc 根据业务类型获取主题
 * @param {Object} params 业务参数
 * @param {Object} options 接口请求相关设置 参考第一个接口的说明
 */
export function queryFlowStyleConfig(params, bizType, flowNo) {
  return commService(`flow/queryFlowStyleConfig/${bizType}/${flowNo}`, params, {
    method: 'GET'
  });
}

/**
 * @desc 初始化业务流程实例
 * @param {Object} params 业务参数
 * @param {Object} options 接口请求相关设置 参考第一个接口的说明
 */
export function flowInit(params, options) {
  return commService('flow/insInit', params, options);
}

/**
 * @desc 初始化业务流程实例
 * @param {Object} params 业务参数
 * @param {Object} options 接口请求相关设置 参考第一个接口的说明
 */
export function flowInitV2(params, options) {
  return commService('flow/insInitV2', params, options);
}

/**
 * @desc 根据业务类型获取业务名称
 * @param {Object} params 业务参数
 * @param {Object} options 接口请求相关设置 参考第一个接口的说明
 */
export function getBizName(params) {
  return commService('business/getBizName', params, {
    method: 'GET',
    loading: false
  });
}

export function getImgCode(params, options) {
  return commService(
    'captcha',
    params,
    Object.assign(
      {
        method: 'GET'
      },
      options
    )
  );
}

/**
 * @desc 获取业务介绍
 * @param {Object} params 业务参数
 * @param {Object} options 接口请求相关设置 参考第一个接口的说明
 */
export function queryBusinessIntroduce(params) {
  return commService('business/queryBusinessIntroduce', params, {
    method: 'GET'
  });
}

/**
 * @desc 首页
 * @param {Object} params 业务参数
 * @param {Object} options 接口请求相关设置 参考第一个接口的说明
 */
export function queryPortalList(params) {
  return commService('business/queryPortalList', params, {
    method: 'GET',
    filter: true,
    loading: false
  });
}

/**
 * @desc 密码同步类别查询
 * @param {Object} params 业务参数
 * @param {Object} options 接口请求相关设置 参考第一个接口的说明
 */
export function pwdSyncTypeQry(params) {
  return commService('account/pwdSyncTypeQry', params, {
    method: 'GET',
    filter: true,
    loading: false
  });
}

/**
 * @desc 登录接口
 * @param {Object} params 业务参数
 * @param {Object} options 接口请求相关设置 参考第一个接口的说明
 */
export function login(params) {
  return commService(
    'account/login',
    { ...params, merchant_id: $hvue.customConfig.merchantId },
    {
      filter: true
    }
  );
}

/**
 * @desc 档案查询
 * @param {Object} params 业务参数
 */
export function archivesInfoQry(params, options = {}) {
  return commService(
    'business/archivesInfoQry',
    params,
    Object.assign(
      {
        method: 'GET'
      },
      options
    )
  );
}

/**
 * @desc 文件上传
 * @param {Object} params 业务参数
 */
export function imageUpload(params, options = {}) {
  return commService('media/imageUpload', params);
}

/**
 * @desc 文件上传
 * @param {Object} params 业务参数
 */
export function uploadWithMultipartFile(params, options = {}) {
  return commService(
    'media/uploadWithMultipartFile',
    params,
    Object.assign(
      {
        method: 'POST'
      },
      options
    )
  );
}

/**
 * @desc 前置条件查询
 * @param {Object} params 业务参数
 */
export function baseInfoCheck(params) {
  return commService('business/preConditionCheck', params, {
    method: 'GET'
  });
}

/**
 * @desc 机构前置条件查询
 * @param {Object} params 业务参数
 */
export function orgPreConditionCheck(params) {
  return commService('business/orgPreConditionCheck', params, {
    method: 'GET',
    loading: false
  });
}

/**
 * @desc 查询ip是否在营业部ip里
 * @param {Object} params 业务参数
 */
export function branchIpMatch(params) {
  return commService('sysBranchInfo/branchIpMatch', params, {
    method: 'GET',
    loading: false
  });
}

/**
 * @desc 业务准入条件检查
 * @param {Object} params 业务参数
 */
export function businessEgliCheck(params) {
  return commService('business/businessEgliCheck', params, {});
}

/**
 * @desc 业务准入留痕
 * @param {Object} params 业务参数
 */
export function questionSave(params) {
  return commService('client/questionSave', params, {});
}

/**
 * @desc 股东账户查询
 * @param {Object} params 业务参数
 * @param {Object} options 添加配置参数
 */
export function stockAccountQry(params, options = {}) {
  return commService('account/stockAccountQry', params, options);
}

/**
 * @desc 北京市场股转账户账户查询
 * @param {Object} params 业务参数
 * @param {Object} options 添加配置参数
 */
export function gjBjsStockAccountSelect(params, options = {}) {
  return commService('account/gjBjsStockAccountSelect', params, options);
}

/**
 * @desc 选择账户查询
 * @param {Object} params 业务参数
 * @param {Object} options 添加配置参数
 */
export function stockAccountSelect(params, options = {}) {
  return commService('account/stockAccountSelect', params, options);
}

/*
 * @desc 单向视频获取jwetoken
 * @param {Object} params 业务参数
 */
export function videoGetJwtToken(params) {
  return commService('video/getJwtToken', params, {
    method: 'GET'
  });
}

/*
 * @desc 对接商照获取createToken
 * @param {Object} params 业务参数
 */
export function createToken(params) {
  return commService('common/createToken', params, {
    method: 'GET',
    loading: false
  });
}

/*
 * @desc 对接商照获取结果
 * @param {Object} params 业务参数
 */
export function getSignResult(params) {
  return commService('common/getSignResult', params, {
    method: 'GET',
    loading: false
  });
}

/*
 * @desc 单向视频注册
 * @param {Object} params 业务参数
 */
export function videoOneRegist(params) {
  return commService('video/addAuthFace', params, { filter: true });
}

/*
 * @desc 获取单向视频结果
 * @param {Object} params 业务参数
 */
export function getVideoOneResult(params) {
  return commService('witness/one/result', params, { filter: true });
}

/**
 * @desc 适当性匹配查询
 * @param {Object} params 业务参数
 */
export function investProInfoQry(params, options) {
  return commService(
    'client/investProInfoQry',
    params,
    Object.assign(
      {
        method: 'GET'
      },
      options
    )
  );
}

/**
 * @desc 适当性匹配查询
 * @param {Object} params 业务参数
 */
export function investProInfoQryV2(params, options) {
  return commService(
    'client/investProInfoQryV2',
    params,
    Object.assign(
      {
        method: 'GET'
      },
      options
    )
  );
}

/**
 * @desc 问卷查询
 * @param {Object} params 业务参数
 */
export function questionQry(params) {
  return commService('egli/questionQry', params, {
    method: 'GET'
  });
}

/**
 * @desc 问卷查询
 * @param {Object} params 业务参数
 */
export function questionQryV2(params) {
  return commService('egli/questionQryV2', params, {
    method: 'GET'
  });
}

/**
 * @desc 问卷查询
 * @param {Object} params 业务参数
 */
export function questionQryV3(params) {
  return commService('egli/questionQryV3', params, {
    method: 'GET'
  });
}

/**
 * @desc 风险测评问卷答题提交
 * @param {Object} params 业务参数
 */
export function questionSubmit(params) {
  return commService('egli/questionSubmit', params);
}

/**
 * @desc 风险测评问卷答题提交
 * @param {Object} params 业务参数
 */
export function questionSubmitV2(params) {
  return commService('egli/questionSubmitV2', params);
}

/**
 * @desc 征信测评问卷答题提交
 * @param {Object} params 业务参数
 */
export function creditInvestigationSubmit(params) {
  return commService('egli/creditInvestigationSubmit', params);
}

/**
 * @desc 本地问卷剩余次数查询
 * @param {Object} params 业务参数
 */
export function getQuestionNumber(params, options) {
  return commService(
    'egli/getQuestionNumber',
    params,
    Object.assign(
      {
        method: 'GET'
      },
      options
    )
  );
}

/**
 * @desc 机构身份信息对比
 * @param {Object} params 业务参数
 */
export function orgIdentification(params) {
  return commService('client/orgIdentification', params);
}

/**
 * @desc 商照信息和柜台对比
 * @param {Object} params 业务参数
 */
export function confirmBusinessLicense(params) {
  return commService('client/confirmBusinessLicense', params);
}

/**
 * @desc 身份信息对比
 * @param {Object} params 业务参数
 */
export function confirmClientInfo(params) {
  return commService('client/confirmClientInfo', params);
}

/**
 * @desc 校验签署密码
 * @param {Object} params 业务参数
 */
export function validationElectronAgreePwd(params) {
  return commService('client/validationElectronAgreePwd', params);
}

/**
 * @desc 查询是否已开通期权账户
 * @param {Object} params 业务参数
 */
export function checkOpenAccount(params) {
  return commService('business/businessHandleInspection', params, {
    method: 'GET',
    loading: false
  });
}

/**
 * @desc 评级授信
 * @param {Object} params 业务参数
 */
export function getCreditApplyQry(params) {
  return commService('client/getCreditApplyQry', params, {
    method: 'GET'
  });
}

/**
 * @desc 发送短信验证码
 * @param {Object} params 业务参数
 */
export function smsSend(params, options = {}) {
  return commService('safe/send', params, options);
}

/**
 * @desc 发送短信验证码(需要图片验证码)
 * @param {Object} params 业务参数
 */
export function safeSmsSend(params, options = {}) {
  return commService('safe/smsSend', params, options);
}

/**
 * @desc 短信验证码校验
 * @param {Object} params 业务参数
 */
export function smsCheck(params, options = {}) {
  return commService('safe/smsCheck', params, options);
}
/**
 * @desc 对比身份证照片和证件照
 * @param {Object} params 业务参数
 */
export function faceCompare(params) {
  return commService('video/faceCompare', params);
}
/**
 * @desc 修改上下文字段
 * @param {Object} params 业务参数
 */
export function updateFlowForm(params) {
  return commService('flow/updateFlowForm', params);
}
/**
 * @desc 查询营业部
 * @param {Object} params 业务参数
 */
export function getSysBranchInfo(params, options) {
  return commService(
    'sysBranchInfo/query',
    params,
    Object.assign(
      {
        method: 'GET',
        loading: false
      },
      options
    )
  );
}
/**
 * @desc 获取追保邮箱
 * @param {Object} params 业务参数
 */
export function generateRecoveryEmail(params, options = {}) {
  return commService(
    '/client/generateRecoveryEmail',
    params,
    Object.assign(
      {
        method: 'GET'
      },
      options
    )
  );
}
/**
 * @desc 银行列表
 * @param {Object} options 配置参数
 */
export function getBank(options = {}, params = {}) {
  return commService(
    'account/getBank',
    params,
    Object.assign(
      {
        method: 'GET'
      },
      options
    )
  );
}
/**
 * @desc 提交申请
 * @param {Object} params 业务参数
 */
export function submitAppointment(params, options) {
  return commService('flow/submitAppointment', params, options);
}
/**
 * @desc 查询jwtToken
 * @param {Object} params 业务参数
 */
export function getJwtToken(params) {
  return commService('agree/getJwtToken', params, {
    method: 'GET',
    allowRepeat: true
  });
}
/**
 * @desc 查询协议
 * @param {Object} params 业务参数
 */
export function queryAgreement(params) {
  return commService('agree/queryAgreement', params, {
    method: 'GET',
    loading: false
  });
}
/**
 * @desc 签署协议
 * @param {Object} params 业务参数
 */
export function doAgreementRecord(params) {
  return commService('agree/doAgreementRecord', params, { allowRepeat: true });
}

/**
 * @desc 查询协议V2
 * @param {Object} params 业务参数
 */
export function queryAgreementExt(params) {
  return commService('agree/queryAgreementExt', params, {
    method: 'GET',
    loading: false
  });
}
/**
 * @desc 签署协议V2
 * @param {Object} params 业务参数
 */
export function doAgreementRecordExt(params) {
  return commService('agree/doAgreementRecordExt', params, {
    allowRepeat: true
  });
}

/**
 * @desc 查询流程审核进度
 * @param {Object} params 业务参数
 */
export function queryIns(params) {
  return commService('flow/queryIns', params);
}

/*
 * @desc 向集中视频注册
 * @param {Object} params 业务参数
 */
export function videoRegist(params) {
  return commService('video/regist', params, { filter: true });
}

/*
 * @desc 向集中视频注册
 * @param {Object} params 业务参数
 */
export function getVideoResult(params) {
  return commService('video/result', params, { filter: true });
}

/*
 * @desc 查询可办理业务
 * @param {Object} params 业务参数
 */
export function businessCanQry(params) {
  return commService('business/businessCanQry', params, {
    method: 'GET',
    loading: false
  });
}

/*
 * @desc 查询IP
 * @param {Object} params 业务参数
 */
export function getUserIp(params) {
  return commService('sysBranchInfo/getUserIp', params, {
    method: 'GET'
  });
}

/*
 * @desc 根据地址拆分省市区
 * @param {Object} params 业务参数
 */
export function addressParse(params) {
  return commService('common/addressParse', params, {
    method: 'GET',
    loading: false
  });
}

/*
 * @desc 查询是否完成过签署密码
 * @param {Object} params 业务参数
 */
export function checkElectronAgreePwd(params) {
  return commService('client/checkElectronAgreePwd', params, {
    method: 'GET'
  });
}

/*
 * @desc 保存签署密码
 * @param {Object} params 业务参数
 */
export function saveElectronAgreePwd(params) {
  return commService('client/saveElectronAgreePwd', params);
}

/*
 * @desc 查询绑定的银行卡
 * @param {Object} params 业务参数
 */
export function bindedBankQry(params) {
  return commService('account/bindedBankQry', params, {
    method: 'GET'
  });
}

/*
 * @desc 统一登录接口
 * @param {Object} params 业务参数
 */
export function ssoLogin(params) {
  return commService('account/ssoLogin', params);
}

/**
 * @desc 股转查询可开通权限
 * @param {Object} params 业务参数
 */
export function accPermissionSelect(params) {
  return commService('account/accPermissionSelect', params);
}

/**
 * @desc 业务办理进度查询
 * @param {Object} params 业务参数
 */
export function businessProcessingProgressQry(params) {
  return commService('business/businessProcessingProgressQry', params, {
    method: 'GET'
  });
}

/**
 * @desc 基金公司信息查询
 * @param {Object} params 业务参数
 */
export function fundCompanyList(params) {
  return commService('account/fundCompanyList', params, {
    method: 'GET'
  });
}

/**
 * @desc 客户已开基金公司账户查询
 * @param {Object} params 业务参数
 */
export function openFundAccountQry(params) {
  return commService('account/openFundAccountQry', params);
}

/**
 * @desc 查询已开通委托方式
 * @param {Object} params 业务参数
 */
export function entrustmentOpenedQry(params) {
  return commService('client/entrustmentOpenedQry', params);
}

/**
 * @desc 找回账号前置检查
 * @param {Object} params 业务参数
 */
export function retrieveAccountPreCheck(params) {
  return commService('client/retrieveAccountPreCheck', params);
}

/**
 * @desc 找回账号检查
 * @param {Object} params 业务参数
 */
export function retrieveAccountCheck(params) {
  return commService('client/retrieveAccountCheck', params);
}

/**
 * @desc 已开通权限查询
 * @param {Object} params 业务参数
 */
export function stockAccountRightQry(params) {
  return commService('account/stockAccountRightQry', params);
}

/**
 * @desc 办理中权限查询
 * @param {Object} params 业务参数
 */
export function processingFundAccountQry(params) {
  return commService('account/processingFundAccountQry', params);
}

/**
 * @desc 已办理进度详情
 * @param {Object} params 业务参数
 */
export function businessProcessingProgressDetailQry(params) {
  return commService('business/businessProcessingProgressDetailQry', params);
}

/**
 * @desc 新增视频观看记录
 * @param {Object} params 业务参数
 */
export function videoWatchAdd(params) {
  return commService('videoWatch/add', params);
}

/**
 * @desc 查询视频观看记录列表
 * @param {Object} params 业务参数
 */
export function videoWatchList(params) {
  return commService('videoWatch/list', params);
}

/**
 * @desc 涉税信息查询
 * @param {Object} params 业务参数
 */
export function getRevenueResidentType(params) {
  return commService('client/getRevenueResidentType', params, {
    method: 'GET'
  });
}

/**
 * @desc 业务策略检查
 * @param {Object} params 业务参数
 */
export function businessStrategyCheck(params) {
  return commService('business/businessStrategyCheck', params);
}

/**
 * @desc 问卷回访获取题目
 * @param {Object} params 业务参数
 */
export function qusetionVisitQuery(params) {
  return commService('returnVisit/questionQry', params, {
    method: 'GET'
  });
}

/**
 * @desc 问卷回访提交
 * @param {Object} params 业务参数
 */
export function qusetionVisitSubmit(params) {
  return commService('returnVisit/questionSubmit', params);
}

/**
 * @desc 生成问卷回访
 * @param {Object} params 业务参数
 */
export function returnVisitGenerate(params) {
  return commService('returnVisit/generate', params);
}

/**
 * @desc 问卷回访home页提示
 * @param {Object} params 业务参数
 */
export function remind(params) {
  return commService('returnVisit/remind', params, {
    method: 'GET'
  });
}

/**
 * @desc 问卷回访list
 * @param {Object} params 业务参数
 */
export function returnVisitList(params) {
  return commService('returnVisit/list', params, {
    method: 'GET'
  });
}

/**
 * @desc 问卷回访用户状态查询
 * @param {Object} params 业务参数
 */
export function visitClientQuery(params) {
  return commService('returnVisit/query', params, {
    method: 'GET'
  });
}

/**
 * @desc 查询风险测评匹配不匹配权限
 * @param {Object} params 业务参数
 */
export function riskMatchRight(params) {
  return commService('client/riskMatchRight', params, {
    method: 'GET'
  });
}

/**
 * @desc 查询柜台已有客户风险测评评级
 * @param {Object} params 业务参数
 */
export function riskQuery(params) {
  return commService('client/riskQuery', params, {
    method: 'GET'
  });
}

/**
 * @desc 增开加挂选择账户
 * @param {Object} params 业务参数
 */
export function createAndHangStockAccountQry(params) {
  return commService('account/createAndHangStockAccountQry', params);
}

/**
 * @desc 新三板账户取消检测
 * @param {Object} params 业务参数
 */
export function gzQxCancelCheck(params) {
  return commService('account/gzQxCancelCheck', params, {
    method: 'GET'
  });
}

/**
 * @desc 销户账户选择
 * @param {Object} params 业务参数
 */
export function cancelAccountSelect(params) {
  return commService('account/cancelAccountSelect', params, {
    method: 'GET'
  });
}

/**
 * @desc 销户检测
 * @param {Object} params 业务参数
 */
export function cancelAccCheck(params) {
  return commService('account/cancelAccCheck', params, {
    method: 'GET'
  });
}

/**
 * @desc 查询视频列表
 * @param {Object} params 业务参数
 */
export function videoList(params) {
  return commService('videoWatch/videoList', params, {
    method: 'GET'
  });
}

/**
 * @desc 查询两融流程查询
 * @param {Object} params 业务参数
 */
export function dfFlowQry(params) {
  return commService('videoWatch/flowQry', params, {
    method: 'GET'
  });
}

/**
 * @desc 查询两融流程记录
 * @param {Object} params 业务参数
 */
export function dfFlowSave(params) {
  return commService('videoWatch/flowSave', params);
}

/**
 * @desc 查询两融流程提交
 * @param {Object} params 业务参数
 */
export function dfFlowSubmit(params) {
  return commService('videoWatch/flowSubmit', params);
}

/**
 * @desc 查询流程实例详情
 * @param {Object} params 业务参数
 */
export function flowInsDetail(params) {
  return commService('flow/flowInsDetail', params);
}

/**
 * @desc 查询用户头像
 * @param {Object} params 业务参数
 */
export function getClientArchiveFileExt(params) {
  return commService('media/getClientArchiveFileExt', params, {
    method: 'GET'
  });
}

/**
 * @desc 登录token认证
 * @param {Object} params 业务参数
 */
export function tokenCheck(params) {
  return commService('token/check', params);
}

/**
 * @desc 东方查询营业部
 * @param {Object} params 业务参数
 */
export function dfBranchInfoQry(params) {
  return commService('branch/branchInfoQry', params, {
    method: 'GET'
  });
}

/**
 * @desc 东方查询上下文key
 * @param {Object} params 业务参数
 */
export function dfFlowDataQry(params) {
  return commService('videoWatch/flowDataQry', params, {
    method: 'GET'
  });
}

/**
 * @desc 东方两融重新办理
 * @param {Object} params 业务参数
 */
export function dfFlowReprocess(params) {
  return commService('videoWatch/flowReprocess', params);
}

/**
 * @desc 东方活体协议留痕
 * @param {Object} params 业务参数
 */
export function biopsyRecord(params) {
  return commService('videoWatch/biopsyRecord', params);
}

/**
 * @desc 东方办理进度查询
 * @param {Object} params 业务参数
 */
export function acceptancFormListQuery(params) {
  return commService('business/acceptancFormListQuery', params, {
    method: 'GET'
  });
}

/**
 * @desc 信用账户密码校验
 * @param {Object} params 业务参数
 * @param {Object} options 添加配置参数
 */
export function creditAndFundPwdCheck(params, options = {}) {
  return commService('account/creditAndFundPwdCheck', params, {
    method: 'GET'
  });
}

/**
 * @desc 信用账户密码校验
 * @param {Object} params 业务参数
 * @param {Object} options 添加配置参数
 */
export function creditPwdCheck(params, options = {}) {
  return commService('account/creditPwdCheck', params);
}

/**
 * @desc 专业投资者时限
 * @param {Object} params 业务参数
 * @param {Object} options 添加配置参数
 */
export function whetherProfExtDate(params, options = {}) {
  return commService('client/whetherProfExtDate', params, {
    method: 'GET'
  });
}

/**
 * @desc 是否当天开户标识
 * @param {Object} params 业务参数
 * @param {Object} options 添加配置参数
 */
export function judgeTodayOpenDate(params, options = {}) {
  return commService('client/JudgeTodayOpenDate', params, {
    method: 'GET'
  });
}

/**
 * @desc 专业投资者时限
 * @param {Object} params 业务参数
 * @param {Object} options 添加配置参数
 */
export function accountConditionCheck(params, options = {}) {
  return commService('account/accountConditionCheck', params, {
    method: 'GET'
  });
}

/**
 * @desc 客户关键信息留痕
 * @param {Object} params 业务参数
 * @param {Object} options 添加配置参数
 */
export function addClientCritMark(params, options = {}) {
  return commService('client/addClientCritMark', params);
}

/**
 * @desc 客户信息留痕(通用)
 * @param {Object} params 业务参数
 * @param {Object} options 添加配置参数
 */
export function addCommonMark(params, options = {}) {
  return commService('client/addCommonMark', params);
}

/**
 * @desc 客户关键信息留痕
 * @param {Object} params 业务参数
 * @param {Object} options 添加配置参数
 */
export function critMarkQuery(params, options = {}) {
  return commService('client/critMarkQuery', params, {
    method: 'GET'
  });
}

/**
 * @desc 客户关键信息留痕
 * @param {Object} params 业务参数
 * @param {Object} options 添加配置参数
 */
export function questionQuery(params, options = {}) {
  return commService('client/questionQuery', params, {
    method: 'GET'
  });
}

/**
 * @desc TA账户查询
 * @param {Object} params 业务参数
 * @param {Object} options 添加配置参数
 */
export function taAccountSelect(params, options = {}) {
  return commService('account/taAccountSelect', params, {
    method: 'GET'
  });
}

/**
 * @desc TA账户办理中查询
 * @param {Object} params 业务参数
 * @param {Object} options 添加配置参数
 */
export function taOnRoadFormQuery(params, options = {}) {
  return commService('account/taOnRoadFormQuery', params, {
    method: 'GET'
  });
}

/**
 * @desc TA账户对应市场股东账户查询
 * @param {Object} params 业务参数
 * @param {Object} options 添加配置参数
 */
export function stockAccountListV1(params, options = {}) {
  return commService('account/stockAccountListV1', params, {
    method: 'GET'
  });
}

/**
 * @desc 对应市场股东账户查询
 * @param {Object} params 业务参数
 * @param {Object} options 添加配置参数
 */
export function stockAccountList(params, options = {}) {
  return commService('account/stockAccountList', params, {
    method: 'GET'
  });
}

/**
 * @desc 期权额度账户查询
 * @param {Object} params 业务参数
 * @param {Object} options 添加配置参数
 */
export function optionAccountInfoQuery(params, options = {}) {
  return commService('account/optionAccountInfoQuery', params, {
    ...options,
    method: 'GET'
  });
}

/**
 * @desc 期权市场额度信息查询
 * @param {Object} params 业务参数
 * @param {Object} options 添加配置参数
 */
export function optionMarketQuotaInfoQuery(params, options = {}) {
  return commService('account/optionMarketQuotaInfoQuery', params, {
    ...options,
    method: 'GET'
  });
}

/**
 * @desc 生成开户确认单
 * @param {Object} params 业务参数
 * @param {Object} options 添加配置参数
 */
export function generateAccountConfirmForm(params, options = {}) {
  return commService('account/generateAccountConfirmForm', params, {
    ...options,
    method: 'GET'
  });
}

/**
 * @desc 开户确认单发送
 * @param {Object} params 业务参数
 * @param {Object} options 添加配置参数
 */
export function sendAccountConfirmForm(params, options = {}) {
  return commService('account/sendAccountConfirmForm', params, options);
}

/**
 * @desc 开户确认单校验
 * @param {Object} params 业务参数
 * @param {Object} options 添加配置参数
 */
export function accountConfirmFormCheck(params, options = {}) {
  return commService('account/accountConfirmFormCheck', params, options);
}

/**
 * @desc 获取客户图片档案信息（免冠照）
 * @param {Object} params 业务参数
 * @param {Object} options 添加配置参数
 */
export function customerImageDownload(params, options = {}) {
  return commService('business/customerImageDownload', params, {
    ...options,
    method: 'GET'
  });
}

/**
 * @desc 期权资金账户检测
 * @param {Object} params 业务参数
 * @param {Object} options 添加配置参数
 */
export function optionAccountInfoCheck(params, options = {}) {
  return commService('account/optionAccountInfoCheck', params, {
    ...options,
    method: 'GET',
    filter: true
  });
}

/**
 * @desc 查询公安照图片
 * @param {Object} params 业务参数
 * @param {Object} options 添加配置参数
 * @returns {Object} data 响应参数
 * - data.policeImageFlag 是否存在公安照：0-不存在公安照；1-存在公安照
 * - data.faceCompareFlag 人脸对比是否一致 0否 1是
 */
export function securityAvatarQry(params, options = {}) {
  return commService('business/securityAvatarQry', params, {
    ...options,
    method: 'GET'
  });
}

/**
 * @desc 结果页无配置流程任务提交
 * @param {Object} params 业务参数
 * @param {Object} options 添加配置参数
 */
export function processTaskFlowSubmit(params, options = {}) {
  return commService('flow/processTaskFlowSubmit', params, {
    ...options
  });
}

/**
 * @desc 短信验证码发送
 * @param {Object} params 业务参数
 * @param {Object} options 添加配置参数
 */
export function smsCodeSending(params, options = {}) {
  return commService('sms/codeSending', params, {
    ...options,
    method: 'GET'
  });
}

/**
 * @desc 短信验证码发送[不需要登录/不校验]
 * @param {Object} params 业务参数
 * @param {Object} options 添加配置参数
 */
export function codeSending(params, options = {}) {
  return commService('sms/check/codeSending', params, {
    ...options,
    method: 'GET'
  });
}

/**
 * @desc 短信验证码校验
 * @param {Object} params 业务参数
 * @param {Object} options 添加配置参数
 */
export function smsCodeVerification(params, options = {}) {
  return commService('sms/codeVerification', params, {
    ...options
  });
}

/**
 * @desc 资金账户信息查询
 * @param {Object} params 业务参数
 * @param {Object} options 添加配置参数
 */
export function fundAccountListQry(params, options = {}) {
  return commService('account/fundAccountListQry', params, {
    ...options,
    method: 'GET'
  });
}

/**
 * @desc 资金账户身份校验
 * @param {Object} params 业务参数
 * @param {Object} options 添加配置参数
 */
export function resetPasswordAuth(params, options = {}) {
  return commService('client/resetPasswordAuth', params, {
    ...options
  });
}

/**
 * @desc 客户基本信息查询
 * @param {Object} params 业务参数
 */
export function clientInfoQryV2(params, options = {}) {
  return commService(
    'client/clientInfoQryV2',
    params,
    Object.assign(
      {
        method: 'GET'
      },
      options
    )
  );
}

/**
 * @desc 合格投资者选择认定状态列表
 * @param {Object} params 业务参数
 * @param {Object} options 添加配置参数
 */
export function bussinesInvestorRightQry(params, options = {}) {
  return commService('business/investorRightQry', params, {
    method: 'GET',
    ...options
  });
}

/**
 * @desc 根据地址获取邮箱
 * @param {Object} params 业务参数
 * @param {Object} options 添加配置参数
 */
export function getZipCode(params, options = {}) {
  return commService('common/queryXzqyPostInfo', params, {
    method: 'GET',
    ...options
  });
}

/**
 * @desc 上传视频
 * @param {Object} params 业务参数
 * @param {Object} options 添加配置参数
 */
export function videoUpload(params, options = {}) {
  return commService('video/upload', params, {
    ...options
  });
}

/**
 * @desc 查询业务名称
 * @param {Object} params 业务参数
 * @param {Object} options 添加配置参数
 */
export function permissionTypeNameQry(params, options = {}) {
  return commService('business/permissionTypeNameQry', params, {
    method: 'GET',
    ...options
  });
}

/**
 * @desc 国金短信验证码发送
 * @param {Object} params 业务参数
 * @param {Object} options 添加配置参数
 */
export function smsCheckCodeSend(params, options = {}) {
  return commService('sms/check/codeSending', params, {
    ...options,
    method: 'GET'
  });
}

/**
 * @desc 检查手机号绑定客户数量
 * @param {Object} params 业务参数
 * @param {Object} options 添加配置参数
 */
export function checkMobileTelNum(params, options = {}) {
  return commService('client/checkMobileTelNum', params, {
    ...options,
    method: 'GET'
  });
}

/**
 * @desc 联系电话或手机号码上报
 * @param {Object} params 业务参数
 * @param {Object} options 添加配置参数
 */
export function mobileTelReport(params, options = {}) {
  return commService('client/mobileTelReport', params, {
    ...options
  });
}

/**
 * @desc 查询电子对账单当天提交次数
 * @param {Object} params 业务参数
 * @param {Object} options 添加配置参数
 */
export function dzdzdConfirmFormCheck(params, options = {}) {
  return commService('account/dzdzdConfirmFormCheck', params, {
    method: 'GET',
    ...options
  });
}

/**
 * @desc 期权账户开通信息查询
 * @param {Object} params 业务参数
 * @param {Object} options 添加配置参数
 */
export function optionAccountQuery(params, options = {}) {
  return commService('account/optionAccountQuery', params, {
    ...options,
    method: 'GET'
  });
}

/**
 * @desc 期权开户账户检测
 * @param {Object} params 业务参数
 * @param {Object} options 添加配置参数
 */
export function optionAccountOpenCheck(params, options = {}) {
  return commService('account/optionAccountOpenCheck', params, {
    ...options,
    method: 'GET'
  });
}

/**
 * @desc 客户期权风险等级查询
 * @param {Object} params 业务参数
 * @param {Object} options 添加配置参数
 */
export function customerOptionRiskLevelQuery(params, options = {}) {
  return commService('account/customerOptionRiskLevelQuery', params, {
    ...options,
    method: 'GET'
  });
}

/**
 * @desc 国金短信验证码校验
 * @param {Object} params 业务参数
 * @param {Object} options 添加配置参数
 */
export function smsCheckCodeVer(params, options = {}) {
  return commService('sms/check/codeVerification', params, {
    ...options
  });
}

/**
 * @desc 客户撤销销户请求
 * @param {Object} params 业务参数
 * @param {Object} options 添加配置参数
 */
export function detainTaskCancel(params, options = {}) {
  return commService('xh/detainTaskCancel', params, {
    ...options
  });
}

/**
 * @desc 查询挽留任务信息
 * @param {Object} params 业务参数
 * @param {Object} options 添加配置参数
 */
export function detainTaskInfoQry(params, options = {}) {
  return commService('xh/detainTaskInfoQry', params, {
    ...options,
    method: 'GET'
  });
}

/**
 * @desc 查询销户账号数据
 * @param {Object} params 业务参数
 * @param {Object} options 添加配置参数
 */
export function xhAccountQuery(params, options = {}) {
  return commService('xh/accountQuery', params, {
    ...options,
    method: 'GET'
  });
}

/**
 * @desc 销户账户选择
 * @param {Object} params 业务参数
 * @param {Object} options 添加配置参数
 */
export function xhCancelAccountSelect(params, options = {}) {
  return commService('xh/cancelAccountSelect', params, {
    ...options,
    method: 'GET'
  });
}

/**
 * @desc 销户账户选择
 * @param {Object} params 业务参数
 * @param {Object} options 添加配置参数
 */
export function queryOfAccountClosureList(params, options = {}) {
  return commService('xh/queryOfAccountClosureList', params, {
    ...options,
    method: 'GET'
  });
}

/**
 * @desc 销户账户选择
 * @param {Object} params 业务参数
 * @param {Object} options 添加配置参数
 */
export function recordChoiceOfAccountClosure(params, options = {}) {
  return commService('xh/recordChoiceOfAccountClosure', params, {
    ...options,
    method: 'GET'
  });
}

/**
 * @desc 获取上一笔流程详情
 * @param {Object} params 业务参数
 * @param {Object} options 添加配置参数
 */
export function previousAcceptanceFormDataQry(params, options = {}) {
  return commService('business/previousAcceptanceFormDataQry', params, {
    ...options,
    method: 'GET'
  });
}

/**
 * @desc 销户检测
 * @param {Object} params 业务参数
 * @param {Object} options 添加配置参数
 */
export function xhCancelAccCheck(params, options = {}) {
  return commService('xh/cancelAccCheck', params, {
    ...options
  });
}

/**
 * @desc 销户条件检测
 * @param {Object} params 业务参数
 * @param {Object} options 添加配置参数
 */
export function accountClosureCheck(params, options = {}) {
  return commService('xh/accountClosureCheck', params, {
    ...options
  });
}

/**
 * @desc 销户流程信息查询
 * @param {Object} params 业务参数
 * @param {Object} options 添加配置参数
 */
export function xhFlowInfoQry(params, options = {}) {
  return commService('xh/flowInfoQry', params, {
    ...options,
    method: 'GET'
  });
}

/**
 * @desc 更新销户账号数据
 * @param {Object} params 业务参数
 * @param {Object} options 添加配置参数
 */
export function xhChooseAccountUpdate(params, options = {}) {
  return commService('xh/chooseAccountUpdate', params, {
    ...options
  });
}

/**
 * @desc 提交选择销户账户信息接口
 * @param {Object} params 业务参数
 * @param {Object} options 添加配置参数
 */
export function recordOfAccountClosure(params, options = {}) {
  return commService('xh/recordOfAccountClosure', params, {
    ...options
  });
}

/**
 * @desc 查询客户挽留分层数据
 * @param {Object} params 业务参数
 * @param {Object} options 添加配置参数
 */
export function customerRetainLayeredDataQry(params, options = {}) {
  return commService('xh/customerRetainLayeredDataQry', params, {
    method: 'GET',
    ...options
  });
}

/**
 * @desc 作废流程实例
 * @param {Object} params 业务参数
 * @param {Object} options 添加配置参数
 */
export function invalidFlowIns(params, options = {}) {
  return commService('flow/invalidFlowIns', params, {
    ...options
  });
}

/**
 * @desc 程序化问卷提交
 * @param {Object} params 业务参数
 * @param {Object} options 添加配置参数
 */
export function surveySubmit(params, options = {}) {
  return commService('egli/surveySubmit', params, {
    ...options
  });
}

/**
 * @desc 查询程序化问卷流水
 * @param {Object} params 业务参数
 * @param {Object} options 添加配置参数
 */
export function surveyJourQuery(params, options = {}) {
  return commService('egli/surveyJourQuery', params, {
    method: 'GET',
    ...options
  });
}

/**
 * @desc 提交程序化问卷流水
 * @param {Object} params 业务参数
 * @param {Object} options 添加配置参数
 */
export function surveyJourSubmit(params, options = {}) {
  return commService('egli/surveyJourSubmit', params, {
    ...options
  });
}

/**
 * @desc 提交程序化问卷协议签署记录查询
 * @param {Object} params 业务参数
 * @param {Object} options 添加配置参数
 */
export function agreementSignStatus(params, options = {}) {
  return commService('contract/agreementSignStatus', params, {
    method: 'GET',
    ...options
  });
}

/*
 * @desc 指定账户交易
 * @param {Object} params 业务参数
 * @param {Object} options 添加配置参数
 */
export function bkAppointTradeQry(params, options = {}) {
  return commService('account/bkAppointTradeQry', params, {
    method: 'GET',
    ...options
  });
}
/**
 * @desc 产品购买双录列表查询
 * @param {Object} params 业务参数：不传双录id查询的是列表，传双录id查询的是指定双录产品的详情信息
 * @param {Object} options 添加配置参数
 */
export function doublerecordQuerylist(params, options = {}) {
  return commService('product/doubleRecord/queryList', params, {
    method: 'GET',
    ...options
  });
}

/**
 * @desc 历史审核通过双录列表查询
 * @param {Object} params 业务参数
 * @param {Object} options 添加配置参数
 */
export function historysuccessDoublerecordQuerylist(params, options = {}) {
  return commService(
    'product/doubleRecord/queryHisList',
    params,
    {
      method: 'GET',
      ...options
    }
  );
}

/**
 * @desc 查询系统参数
 * @param {Object} params 业务参数
 * @param {Object} options 添加配置参数
 */
export function getConfigMap(params, options = {}) {
  return commService('common/config/map', params, {
    method: 'GET',
    ...options
  });
}

/**
 * @desc 期权等级查询
 * @param {Object} params 业务参数
 * @param {Object} options 添加配置参数
 */
export function queryOptionRiskLevelInfo(params, options = {}) {
  return commService('account/queryOptionRiskLevelInfo', params, {
    ...options,
    method: 'GET'
  });
}

/**
 * @desc 问卷调查
 * @param {Object} params 业务参数
 * @param {Object} options 添加配置参数
 */
export function configMap(params, options = {}) {
  return commService('common/config/map', params, {
    ...options,
    method: 'GET'
  });
}

/**
 * @desc 电子协议列表查询
 * @param {Object} params 业务参数
 * @param {Object} options 添加配置参数
 */
export function clientContractSignedQry(params, options = {}) {
  return commService('contract/clientContractSignedQry', params, {
    ...options,
    method: 'GET'
  });
}

/**
 * @desc 三方存管列表页查询接口
 * @param {Object} params 业务参数
 * @param {Object} options 添加配置参数
 */
export function indexquery(params, options = {}) {
  return commService('business/depository/indexquery', params, {
    ...options,
    method: 'GET'
  });
}

/**
 * @desc 查询预约列表
 * @param {Object} params 业务参数
 * @param {Object} options 添加配置参数
 */
export function bcPreBizList(params, options = {}) {
  return commService('business/bcPreBizList', params, {
    method: 'GET',
    ...options
  });
}

/**
 * @desc 查询预约历史记录
 * @param {Object} params 业务参数
 * @param {Object} options 添加配置参数
 */
export function bcPreBizHisList(params, options = {}) {
  return commService('business/bcPreBizHisList', params, {
    method: 'GET',
    ...options
  });
}

/**
 * @desc 关联受理单与预约单
 * @param {Object} params 业务参数
 * @param {Object} options 添加配置参数
 */
export function bcPreBizUpdate(params, options = {}) {
  return commService('business/bcPreBizUpdate', params, {
    ...options
  });
}

/**
 * @desc 查询当前登录用户深市/沪市对应的账户列表
 * @param {Object} params 业务参数
 * @param {Object} options 添加配置参数
 */
export function bkCreditStockAccListQry(params, options = {}) {
  return commService('account/bkCreditStockAccListQry', params, {
    method: 'GET',
    ...options
  });
}

/**
 * @desc 关联关系确认查询
 * @param {Object} params 业务参数
 * @param {Object} options 添加配置参数
 */
export function incidenceRelationInfo(params, options = {}) {
  return commService('incidence/relation/info', params, {
    ...options,
    method: 'GET'
  });
}

/**
 * @desc 获取协议文本内容  协议信息查询
 * @param {Object} params 业务参数
 * @param {Object} options 添加配置参数
 */
export function contractQry(params, options = {}) {
  return commService('contract/contractQry', params, {
    ...options,
    method: 'GET'
  });
}

/**
 * @desc 查询系统参数配置
 * @param {Object} params 业务参数
 * @param {Object} options 添加配置参数
 */
export function querySysConfig(params, options = {}) {
  return commService('common/config/map', params, {
    ...options,
    method: 'GET'
  });
}

/**
 * @desc 获取协议的最新版本   协议基本信息查询
 * @param {Object} params 业务参数
 * @param {Object} options 添加配置参数
 */
export function infoQry(params, options = {}) {
  return commService('contract/infoQry', params, {
    ...options,
    method: 'GET'
  });
}

/**
 * @desc 查询客户的信用股东账户
 * @param {Object} params 业务参数
 * @param {Object} options 添加配置参数
 */
export function creditStockAccListQry(params, options = {}) {
  return commService('account/creditStockAccListQry', params, {
    method: 'GET',
    ...options
  });
}

/**
 * @desc 查询客户当前的信用额度
 * @param {Object} params 业务参数
 * @param {Object} options 添加配置参数
 */
export function creditQuotaQuery(params, options = {}) {
  return commService('account/creditQuotaQuery', params, {
    method: 'GET',
    ...options
  });
}

/**
 * @desc 查询客户的试算授信额度
 * @param {Object} params 业务参数
 * @param {Object} options 添加配置参数
 */
export function calculateCreditQuota(params, options = {}) {
  return commService('account/calculateCreditQuota', params, {
    ...options
  });
}

/**
 * @desc 查询深度信用次数
 * @param {Object} params 业务参数
 * @param {Object} options 添加配置参数
 */
export function deepCreditRecordQuery(params, options = {}) {
  return commService('account/deepCreditRecordQuery', params, {
    method: 'GET',
    ...options
  });
}

/**
 * @desc 提交深度信用次数
 * @param {Object} params 业务参数
 * @param {Object} options 添加配置参数
 */
export function deepCreditSubmit(params, options = {}) {
  return commService('account/deepCreditSubmit', params, {
    ...options
  });
}

/**
 * @desc 提交深度信用次数(提交柜台)
 * @param {Object} params 业务参数
 * @param {Object} options 添加配置参数
 */
export function clientCreditAnswerSubmission(params, options = {}) {
  return commService('account/clientCreditAnswerSubmission', params, {
    ...options
  });
}

/**
 * @desc 报价回购开通/取消校验
 * @param {Object} params 业务参数
 * @param {Object} options 添加配置参数
 */
export function bjhgStockAccountQxCheck(params, options = {}) {
  return commService('account/bjhgStockAccountQxCheck', params, {
    ...options,
    method: 'GET'
  });
}

/**
 * @desc 获取中台的协议信息   根据签署id查询客户签署协议内容
 * @param {Object} params 业务参数
 * @param {Object} options 添加配置参数
 */
export function agreementQryBySignId(params, options = {}) {
  return commService('contract/agreementQryBySignId', params, {
    ...options,
    method: 'GET'
  });
}

/**
 * @desc 对接登录服务获取token
 * @param {Object} params 业务参数
 * @param {Object} options 添加配置参数
 */
export function instantTokenGen(params, options = {}) {
  return commService('client/instantTokenGen', params, {
    ...options
  });
}


/**
 * @desc 养老金中登账号查询
 * @param {Object} params 业务参数
 * @param {Object} options 添加配置参数
 */
export function pensionAccountQry(params, options = {}) {

  return commService('pension/pensionAccountQry', params, {
    ...options,
    method: 'GET'
  });

}

/**
 * @desc 根据协议编号查询协议内容
 * @param {Object} params 业务参数
 * @param {Object} options 添加配置参数
 */
export function agreementQry(params, options = {}) {
  return commService('contract/agreementQry', params, {
    ...options,
    method: 'GET'
  });
}

/**
 * @desc 养老金银行信息查询
 * @param {Object} params 业务参数
 * @param {Object} options 添加配置参数
 */
export function csdcBankQry(params, options = {}) {
  return commService('pension/csdcBankQry', params, {
    ...options,
    method: 'GET'
  });
}

/**
 * @desc 获取信用三方存管列表
 * @param {Object} params 业务参数
 * @param {Object} options 添加配置参数
 */
export function creditIndexquery(params, options = {}) {
  return commService('business/depository/credit/indexquery', params, {
    ...options,
    method: 'GET'
  });
}

/**
 * @desc 获取信用三方存管信用资金账号
 * @param {Object} params 业务参数
 * @param {Object} options 添加配置参数
 */
export function queryCreditFundAccount(params, options = {}) {
  return commService('credit/queryCreditFundAccount', params, {
    ...options,
    method: 'GET'
  });
}

/**
 * @desc 	卡bin查询查询接口
 * @param {Object} params 业务参数
 * @param {Object} options 添加配置参数
 */
export function binquery(params, options = {}) {

  return commService('business/depository/binquery', params, {
    ...options,
    method: 'GET'
  });

}

/**
 * @desc 中签资金账户账户查询
 * @param {Object} params 业务参数
 * @param {Object} options 添加配置参数
 */
export function zqFundAccountQry(params, options = {}) {
  return commService('account/zqFundAccountQry', params, {
    ...options,
    method: 'GET'
  });
}

/**
 * @desc 	养老金中台平台账户开通
 * @param {Object} params 业务参数
 * @param {Object} options 添加配置参数
 */
export function pensionAccountOpen(params, options = {}) {
  return commService('pension/pensionAccountOpen', params, {
    ...options,
  });
}

/**
 * @desc 	养老金中登平台短信验证
 * @param {Object} params 业务参数
 * @param {Object} options 添加配置参数
 */
export function messageCheck(params, options = {}) {
  return commService('pension/messageCheck', params, {
    ...options,
  });
}

/**
 * @desc 	养老金资金账户开通
 * @param {Object} params 业务参数
 * @param {Object} options 添加配置参数
 */
export function fundAccountOpen(params, options = {}) {

  return commService('pension/fundAccountOpen', params, {
    ...options,
  });

}

/**
 * @desc 创业板账户检测
 * @param {Object} params 业务参数
 * @param {Object} options 添加配置参数
 */
export function cybAccountOpenCheck(params, options = {}) {
  return commService('account/cybAccountOpenCheck', params, {
    ...options,
    method: 'GET'
  });
}
/**
 * @desc 信用资金账户查询
 * @param {Object} params 业务参数
 * @param {Object} options 添加配置参数
 */
export function fundAccountLrhyQry(params, options = {}) {
  return commService('account/fundAccountLrhyQry', params, {
    ...options,
    method: 'GET'
  });
}

/**
 * @desc 交易、资金密码校验
 * @param {Object} params 业务参数
 * @param {Object} options 添加配置参数
 */
export function pwdCheck(params, options = {}) {
  return commService('account/pwdCheck', params, {
    ...options
  });
}

/**
 * @desc 找回资金账户伪登录
 * @param {Object} params 业务参数
 * @param {Object} options 添加配置参数
 */
export function retrieveFundAccountCheck(params, options = {}) {
  return commService('client/retrieveFundAccountCheck', params, {
    ...options
  });
}

/**
 * @desc 个人资料中登弹窗留痕
 * @param {Object} params 业务参数
 * @param {Object} options 添加配置参数
 */
export function clientInfoTipOff(params, options = {}) {
  return commService('client/clientInfoTipOff', params, {
    ...options,
    method: 'GET'
  });
}

/**
 * @desc 个人资料中登弹窗确认
 * @param {Object} params 业务参数
 * @param {Object} options 添加配置参数
 */
export function csdcInfoSubmit(params, options = {}) {
  return commService('client/csdcInfoSubmit', params, {
    ...options
  });
}

/**
 * @desc 获取待签署上市公司列表
 * @param {Object} params 业务参数
 * @param {Object} options 添加配置参数
 */
export function eqIncCompanyQry(params, options = {}) {
  return commService('business/eqIncCompanyQry', params, {
    method: 'GET',
    ...options
  });
}

/**
 * @desc 客户分类信息查询
 * @param {Object} params 业务参数
 * @param {Object} options 添加配置参数
 */
export function clientGroupInfoQry(params, options = {}) {
  return commService('account/clientGroupInfoQry', params, {
    method: 'GET',
    ...options
  });
}

/**
 * @desc 验证码验证接口
 * @param {Object} params 业务参数
 * @param {Object} options 添加配置参数
 */
export function codeVerify(params, options = {}) {
  return commService('sms/twoFactor/codeVerify', params, {
    ...options
  });
}

/**
 * @desc 查询业务受理信息
 * @param {Object} params 业务参数
 * @param {Object} options 添加配置参数
 */
export function businessAcceptInfoQry(params, options = {}) {
  return commService('business/businessAcceptInfoQry', params, {
    method: 'GET',
    ...options
  });
}

/**
 * @desc 资金账户信息查询(区分独立秘密)
 * @param {Object} params 业务参数
 * @param {Object} options 添加配置参数
 */
export function fundAccountListQryV1(params, options = {}) {
  return commService('account/fundAccountListQryV1', params, {
    ...options,
    method: 'GET'
  });
}

/**
 * @desc 账户菜单权限查询
 * @param {Object} params 业务参数
 * @param {Object} options 添加配置参数
 */
export function accountMenuPermQuery(params, options = {}) {
  return commService('account/accountMenuPermQuery', params, {
    ...options,
    method: 'GET'
  });
}

/**
 * @desc 策略类型信息查询
 * @param {Object} params 业务参数
 * @param {Object} options 添加配置参数
 */
export function accountStrategyQuery(params, options = {}) {
  return commService('account/accountStrategyQuery', params, {
    ...options,
    method: 'GET'
  });
}

/**
 * @desc 提交预约电话回访
 * @param {Object} params 业务参数
 * @param {Object} options 添加配置参数
 */
export function preCommitAccountCallout(params, options = {}) {
  return commService('account/preCommitAccountCallout', params, {
    ...options
  });
}

/**
 * @desc 找回资金账号列表
 * @param {Object} params 业务参数
 * @param {Object} options 添加配置参数
 */
export function retrieveFundAccountList(params, options = {}) {
  return commService('client/retrieveFundAccountList', params, {
    ...options,
    method: 'GET'
  });
}

/**
 * @desc 查询中登和政通系统是否可用
 * @param {Object} params 业务参数
 * @param {Object} options 添加配置参数
 */
export function statusQuery(params, options = {}) {
  return commService('sys/statusQuery', params, {
    ...options,
    method: 'GET'
  });
}

/**
 * @desc 获取智能单向语音播报题目
 * @param {Object} params 业务参数
 * @param {Object} options 添加配置参数
 */
export function videoGetScriptV2(params, options = {}) {
  return commService('video/getScriptV2', params, {
    ...options,
    method: 'GET'
  });
}

/**
 * @desc 用户凭证登记接口(单向活体)
 * @param {Object} params 业务参数
 * @param {Object} options 添加配置参数
 */
export function videoAddCertificate(params, options = {}) {
  return commService('video/addCertificate', params, { ...options });
}

/**
 * @desc 查询公安校验配置
 * @param {Object} params 业务参数
 * @param {Object} options 添加配置参数
 */
export function securityVerifyConfig(params, options = {}) {
  return commService('business/securityVerifyConfig', params, { ...options, method: 'GET' });
}
/**
 * @desc 查询协议V3版本
 * @param {Object} params 业务参数
 * @param {Object} options 添加配置参数
 */
export function queryAgreementExtV3(params, options = {}) {
  return commService('agree/queryAgreementExtV3', params, { ...options, method: 'GET' });
}

/**
 * @desc 查询顶点协议PDF详情
 * @param {Object} params 业务参数
 * @param {Object} options 添加配置参数
 */
export function queryAgreementPdf(params, options = {}) {
  return commService('agree/queryAgreementPdf', params, { ...options, responseType: 'blob', method: 'GET' });
}

/**
 * @desc 查询协议路径(顶点渠道)
 * @param {Object} params 业务参数
 * @param {Object} options 添加配置参数
 */
export function queryVertexAgreementPath(params, options = {}) {
  return commService('agree/queryVertexAgreement', params, {
    ...options,
    method: 'GET'
  });
}

/**
 * @desc 查询协议路径(顶点渠道)
 * @param {Object} params 业务参数
 * @param {Object} options 添加配置参数
 */
export function queryVertexAgreementHTML(params, options = {}) {
  return axiosRequest('agree/queryVertexAgreement', params, {
    ...options,
    method: 'GET'
  });
}

/**
 * @desc 系统开通前置校验（ptrade/QMT权限开通业务使用）
 * @param {Object} params 业务参数
 * @param {Object} options 添加配置参数
 */
export function sysOpenPreCheck(params, options = {}) {
  return commService('account/sysOpenPreCheck', params, {
    ...options,
    method: 'GET'
  });
}

/**
 * @desc 找回资金账户发送短信
 * @param {Object} params 业务参数
 * @param {Object} options 添加配置参数
 */
export function retrieveFundAccountSendMobile(params, options = {}) {
  return commService('client/retrieveFundAccountSendMobile', params, {
    ...options
  });
}

/**
 * @desc 找回资金账号默认手机号验证码校验
 * @param {Object} params 业务参数
 * @param {Object} options 添加配置参数
 */
export function retrieveFundAccountDefaultMobileCodeVerify(params, options = {}) {
  return commService('client/retrieveFundAccountDefaultMobileCodeVerify', params, {
    ...options
  });
}

/**
 * @desc 找回资金账号默认手机发送验证码
 * @param {Object} params 业务参数
 * @param {Object} options 添加配置参数
 */
export function retrieveFundAccountDefaultMobileSendCode(params, options = {}) {
  return commService('client/retrieveFundAccountDefaultMobileSendCode', params, {
    ...options
  });
}

/**
 * @desc 重置密码回调通知渠道(腾讯自选股&金微蓝)
 * @param {Object} params 业务参数
 * @param {Object} options 添加配置参数
 */
export function resetPasswordCallbackNotify(params, options = {}) {
  return commService('client/resetPasswordCallbackNotify', params, {
    ...options
  });
}

/**
 * @desc 两融预约开户
 * @param {Object} params 业务参数
 * @param {Object} options 添加配置参数
 */
export function saveActivity(params, options = {}) {
  return commService('credit/activity/saveActivity', params, {
    ...options
  });
}

/**
 * @desc 两融预约开户
 * @param {Object} params 业务参数
 * @param {Object} options 添加配置参数
 */
export function detainPredicate(params, options = {}) {
  return commService('credit/activity/detainPredicate',
    params,
    Object.assign(
      {
        method: 'GET'
      },
      options
    ));
  }

/**
 * @desc 两融预约开户
 * @param {Object} params 业务参数
 * @param {Object} options 添加配置参数
 */
export function queryJDMobileInfo(params, options = {}) {
  return commService('account/queryJDMobileInfo', params, {
    ...options
  });
}

/**
 * @desc 获取养老金建行小程序参数
 * @param {Object} params 业务参数
 * @param {Object} options 添加配置参数
 */
export function getPensionCCBMiniAppParams(params, options = {}) {
  return commService('third/getPensionCCBMiniAppParams', params, Object.assign(
    {
      method: 'GET'
    },
    options
  ));
}

/**
 * @desc 期权账户开通信息查询V2
 * @param {Object} params 业务参数
 * @param {Object} options 添加配置参数
 * @returns {Promise<Object>} 返回期权账户信息
 * @returns {string} returns.data.type - 类型：1-开通期权账户；2-展示期权开户指引页面；3-直接进入预约沪市期权功能；4-中登满3户拦截提醒
 * @returns {string} [returns.data.optionFundAccount] - 股票期权资金账户（type=1时返回）
 * @returns {string} [returns.data.acodeAccount] - 一码通账户号码（type=1时返回）
 * @returns {Array<Object>} [returns.data.optionAccountInfoList] - 衍生品账户信息集合（type=1时返回）
 * @returns {string} returns.data.optionAccountInfoList[].exchangeType - 交易类别
 * @returns {string} returns.data.optionAccountInfoList[].assetProp - 资产属性
 * @returns {string} returns.data.optionAccountInfoList[].stockAccount - 股东账号
 * @returns {string} returns.data.optionAccountInfoList[].holderStatus - 股东账户状态
 * @returns {string} returns.data.optionAccountInfoList[].holderStatusDesc - 股东账户状态描述
 * @returns {string} returns.data.optionAccountInfoList[].regflag - 是否指定
 * @returns {string} [returns.data.stockOptionRealSimulationAccount] - 股票期权全真模拟交易账户（type=2时返回）
 * @returns {string} [returns.data.optionSimulationTradingExperience] - 期权模拟交易经历（type=2时返回）
 * @returns {string} [returns.data.optionKnowledgeTestScore] - 期权知识测试成绩（type=2时返回）
 * @returns {string} [returns.data.simulationGuideLink] - 模拟指导链接（type=2时返回）
 * @returns {string} [returns.data.optionEduColumnLink] - 期权投教专栏链接（type=2时返回）
 * @returns {string} [returns.data.knowledgeBookLink] - 知识宝典链接（type=2时返回）
 */
export function optionAccountQueryV2(params, options = {}) {
  return commService('account/optionAccountQueryV2', params, {
    ...options,
    method: 'GET'
  });
}

/**
 * @desc 查询预约时间列表
 * @param {Object} params 业务参数
 * @param {Object} options 接口请求相关设置 参考第一个接口的说明
 */
export function queryAppointmentTime(params, options = {}) {
  return commService(
    'common/queryAppointmentTime',
    params,
    Object.assign(
      {
        method: 'GET',
        loading: false
      },
      options
    )
  );
}

/**
 * @desc 沪市期权预约信息查询
 * @param {Object} params 业务参数
 * @param {Object} options 接口请求相关设置 参考第一个接口的说明
 */
export function queryShOptionAppointmentInfo(params, options = {}) {
  return commService(
    'business/queryShOptionAppointmentInfo',
    params,
    Object.assign(
      {
        method: 'GET',
        loading: false
      },
      options
    )
  );
}

/**
 * @desc 客户挽留分层数据查询
 * @param {Object} params 业务参数
 * @param {Object} options 接口请求相关设置 参考第一个接口的说明
 */
export function xhCustomerRetainLayeredDataQry(params, options = {}) {
  return commService(
    'xh/customerRetainLayeredDataQry',
    params,
    Object.assign(
      {
        method: 'GET'
      },
      options
    )
  );
}

/**
 * @desc 销户流程信息查询
 * @param {Object} params 业务参数
 * @param {Object} options 接口请求相关设置 参考第一个接口的说明
 */
export function xhFlowInfoQryV2(params, options = {}) {
  return commService(
    'xh/flowInfoQryV2',
    params,
    Object.assign(
      {
        method: 'GET'
      },
      options
    )
  );
}

/**
 * @desc 证件上传（包含ocr识别）
 * @param {Object} params 业务参数
 * @param {Object} options 添加配置参数
 */
export function ocrParseIDCard(params, options = {}) {
  return commService('client/ocrParseIDCard', params, {
    ...options
  });
}

/**
 * @desc 挽留任务创建接口
 * @param {Object} params 业务参数
 * @param {Object} options 接口请求相关设置 参考第一个接口的说明
 */
export function xhDetainTaskCreate(params, options = {}) {
  return commService('xh/detainTaskCreate', params, options);
}
